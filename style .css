/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 25%, #fecfef 50%, #ffd1ff 75%, #c2e9fb 100%);
    background-size: 400% 400%;
    animation: gradientShift 8s ease-in-out infinite;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    user-select: none;
    position: relative;
}

/* Beautiful animated gradient background */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* 3D Background Animation Elements */
body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 182, 193, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 218, 185, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(221, 160, 221, 0.3) 0%, transparent 50%);
    animation: float3D 12s ease-in-out infinite;
    z-index: -1;
}

@keyframes float3D {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    33% {
        transform: translateY(-20px) rotate(120deg);
        opacity: 0.9;
    }
    66% {
        transform: translateY(10px) rotate(240deg);
        opacity: 0.8;
    }
}

/* CSS Variables for Themes */
:root {
    --primary-color: #00bcd4;
    --secondary-color: #0097a7;
    --accent-color: #00e5ff;
    --text-color: #ffffff;
    --bg-color: #1a1a2e;
    --glass-bg: rgba(255, 255, 255, 0.1);
    --shadow-color: rgba(0, 0, 0, 0.3);
}

/* Theme Variations */
.theme-red {
    --primary-color: #f44336;
    --secondary-color: #d32f2f;
    --accent-color: #ff5722;
}

.theme-green {
    --primary-color: #4caf50;
    --secondary-color: #388e3c;
    --accent-color: #8bc34a;
}

.theme-purple {
    --primary-color: #9c27b0;
    --secondary-color: #7b1fa2;
    --accent-color: #e91e63;
}

.theme-gold {
    --primary-color: #ff9800;
    --secondary-color: #f57c00;
    --accent-color: #ffc107;
}

/* Watch Container */
.watch-container {
    position: relative;
    perspective: 1000px;
}

.watch-frame {
    width: 480px;
    height: 620px;
    background: linear-gradient(145deg, var(--bg-color), #16213e);
    border-radius: 70px;
    padding: 30px;
    box-shadow:
        0 30px 60px var(--shadow-color),
        inset 0 4px 20px rgba(255, 255, 255, 0.1),
        inset 0 -4px 20px rgba(0, 0, 0, 0.3),
        0 0 50px rgba(255, 182, 193, 0.4);
    position: relative;
    overflow: hidden;
    border: 5px solid var(--primary-color);
    animation: watchGlow 3s ease-in-out infinite alternate, watch3D 6s ease-in-out infinite;
    transform-style: preserve-3d;
    margin-bottom: 50px;
}

@keyframes watchGlow {
    0% {
        box-shadow:
            0 25px 50px var(--shadow-color),
            0 0 30px var(--primary-color),
            0 0 60px rgba(255, 182, 193, 0.3);
    }
    100% {
        box-shadow:
            0 30px 60px var(--shadow-color),
            0 0 40px var(--primary-color),
            0 0 80px rgba(255, 182, 193, 0.5);
    }
}

/* 3D Watch Animation */
@keyframes watch3D {
    0%, 100% {
        transform: rotateY(0deg) rotateX(0deg) translateZ(0px);
    }
    25% {
        transform: rotateY(5deg) rotateX(2deg) translateZ(10px);
    }
    50% {
        transform: rotateY(0deg) rotateX(-2deg) translateZ(5px);
    }
    75% {
        transform: rotateY(-5deg) rotateX(2deg) translateZ(10px);
    }
}

/* Status Bar */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: var(--glass-bg);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    margin-bottom: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.battery, .signal, .bluetooth {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--text-color);
    font-size: 12px;
}

.battery i {
    color: var(--accent-color);
    animation: batteryPulse 2s ease-in-out infinite;
}

@keyframes batteryPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Main Display */
.main-display {
    height: 450px;
    position: relative;
    border-radius: 35px;
    overflow: hidden;
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: inset 0 0 25px rgba(255, 182, 193, 0.2);
}

.time-display {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    background:
        radial-gradient(circle at 30% 20%, rgba(255, 182, 193, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(255, 218, 185, 0.1) 0%, transparent 50%);
    animation: screenGlow 8s ease-in-out infinite alternate;
}

.time-display.active {
    opacity: 1;
    transform: translateX(0);
}

@keyframes screenGlow {
    0% {
        background:
            radial-gradient(circle at 30% 20%, rgba(255, 182, 193, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 70% 80%, rgba(255, 218, 185, 0.1) 0%, transparent 50%);
    }
    50% {
        background:
            radial-gradient(circle at 70% 30%, rgba(221, 160, 221, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 30% 70%, rgba(255, 234, 167, 0.15) 0%, transparent 50%);
    }
    100% {
        background:
            radial-gradient(circle at 50% 10%, rgba(194, 233, 251, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 50% 90%, rgba(255, 183, 197, 0.1) 0%, transparent 50%);
    }
}

/* Digital Time Display */
.time-wrapper {
    text-align: center;
    margin-bottom: 20px;
}

.time-digits {
    font-size: 3.5rem;
    font-weight: 300;
    color: var(--text-color);
    text-shadow: 0 0 20px var(--primary-color);
    animation: timeGlow 2s ease-in-out infinite alternate;
}

@keyframes timeGlow {
    0% { text-shadow: 0 0 20px var(--primary-color); }
    100% { text-shadow: 0 0 30px var(--primary-color), 0 0 40px var(--accent-color); }
}

.separator {
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

.ampm {
    font-size: 1.2rem;
    color: var(--accent-color);
    margin-top: 10px;
}

.date-display {
    text-align: center;
    color: var(--text-color);
}

.day {
    display: block;
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 5px;
    color: var(--accent-color);
}

.date {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Analog Style Digital Display */
.analog-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.clock-face {
    width: 240px;
    height: 240px;
    border: 4px solid var(--primary-color);
    border-radius: 50%;
    position: relative;
    margin: 20px auto;
    background: radial-gradient(circle, var(--glass-bg), transparent);
    backdrop-filter: blur(10px);
    box-shadow:
        0 0 25px var(--primary-color),
        inset 0 0 25px rgba(255, 255, 255, 0.1);
}

/* Clock Center Dot */
.clock-face::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    background: radial-gradient(circle, var(--accent-color), var(--primary-color));
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow:
        0 0 15px var(--accent-color),
        inset 0 2px 5px rgba(255, 255, 255, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.6);
    z-index: 15;
}

@keyframes clockRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hour-markers {
    position: absolute;
    width: 100%;
    height: 100%;
}

.hour-markers::before {
    content: '';
    position: absolute;
    top: 5px;
    left: 50%;
    width: 2px;
    height: 20px;
    background: var(--accent-color);
    transform: translateX(-50%);
    box-shadow:
        0 0 0 0 var(--accent-color),
        0 45px 0 0 var(--accent-color),
        0 90px 0 0 var(--accent-color),
        0 135px 0 0 var(--accent-color),
        45px 22.5px 0 0 var(--accent-color),
        90px 45px 0 0 var(--accent-color),
        135px 67.5px 0 0 var(--accent-color),
        180px 90px 0 0 var(--accent-color);
}

.digital-hands {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.hour-hand, .minute-hand, .second-hand {
    position: absolute;
    transform-origin: bottom center;
    border-radius: 8px;
    transition: all 0.3s ease;
    z-index: 10;
}

.hour-hand {
    width: 8px;
    height: 60px;
    top: -60px;
    left: -4px;
    background: linear-gradient(to top, var(--primary-color), rgba(255, 255, 255, 0.8));
    box-shadow:
        0 0 15px var(--primary-color),
        inset 0 2px 5px rgba(255, 255, 255, 0.3),
        inset 0 -2px 5px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.4);
}

.minute-hand {
    width: 6px;
    height: 85px;
    top: -85px;
    left: -3px;
    background: linear-gradient(to top, var(--secondary-color), rgba(255, 255, 255, 0.9));
    box-shadow:
        0 0 12px var(--secondary-color),
        inset 0 2px 4px rgba(255, 255, 255, 0.4),
        inset 0 -2px 4px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.second-hand {
    width: 3px;
    height: 95px;
    top: -95px;
    left: -1.5px;
    background: linear-gradient(to top, var(--accent-color), #ffffff);
    box-shadow:
        0 0 20px var(--accent-color),
        0 0 40px rgba(255, 255, 255, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.6);
}

@keyframes hourRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes minuteRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes secondRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.center-dot {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 10px;
    height: 10px;
    background: var(--accent-color);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 10px var(--accent-color);
}

.digital-time-small {
    color: var(--text-color);
    font-size: 1.5rem;
    text-shadow: 0 0 10px var(--primary-color);
}

/* Neon Style Display */
.neon-container {
    text-align: center;
}

.neon-time {
    font-size: 4rem;
    font-weight: bold;
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
    gap: 5px;
}

.neon-digit, .neon-separator {
    color: var(--primary-color);
    text-shadow:
        0 0 5px var(--primary-color),
        0 0 10px var(--primary-color),
        0 0 15px var(--primary-color),
        0 0 20px var(--accent-color);
    animation: neonFlicker 2s ease-in-out infinite alternate;
}

@keyframes neonFlicker {
    0%, 100% {
        text-shadow:
            0 0 5px var(--primary-color),
            0 0 10px var(--primary-color),
            0 0 15px var(--primary-color),
            0 0 20px var(--accent-color);
    }
    50% {
        text-shadow:
            0 0 2px var(--primary-color),
            0 0 5px var(--primary-color),
            0 0 8px var(--primary-color),
            0 0 12px var(--accent-color);
    }
}

.neon-date {
    font-size: 1.2rem;
    color: var(--accent-color);
    text-shadow: 0 0 10px var(--accent-color);
}

/* Stopwatch Display */
.stopwatch-container {
    text-align: center;
}

.stopwatch-title {
    font-size: 1rem;
    color: var(--accent-color);
    margin-bottom: 20px;
    letter-spacing: 2px;
}

.stopwatch-time {
    font-size: 3rem;
    color: var(--text-color);
    margin-bottom: 30px;
    text-shadow: 0 0 15px var(--primary-color);
}

.stopwatch-controls {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.sw-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    background: linear-gradient(145deg, var(--primary-color), var(--secondary-color));
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.sw-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
}

.sw-btn:active {
    transform: translateY(0);
}

.sw-btn.start-stop.running {
    background: linear-gradient(145deg, #f44336, #d32f2f);
}

/* Timer Display */
.timer-container {
    text-align: center;
}

.timer-title {
    font-size: 1rem;
    color: var(--accent-color);
    margin-bottom: 20px;
    letter-spacing: 2px;
}

.timer-time {
    font-size: 3rem;
    color: var(--text-color);
    margin-bottom: 20px;
    text-shadow: 0 0 15px var(--primary-color);
}

.timer-controls {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-bottom: 20px;
}

.timer-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 20px;
    background: linear-gradient(145deg, var(--primary-color), var(--secondary-color));
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

.timer-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
}

.timer-progress {
    width: 200px;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    margin: 0 auto;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 3px;
    transition: width 1s ease;
    box-shadow: 0 0 10px var(--primary-color);
}

/* Weather Display */
.weather-container {
    text-align: center;
    color: var(--text-color);
}

.weather-icon {
    font-size: 4rem;
    margin-bottom: 15px;
    color: var(--accent-color);
    animation: weatherFloat 3s ease-in-out infinite;
}

@keyframes weatherFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

.weather-temp {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 10px;
    text-shadow: 0 0 15px var(--primary-color);
}

.weather-desc {
    font-size: 1.2rem;
    margin-bottom: 20px;
    color: var(--accent-color);
}

.weather-details {
    display: flex;
    justify-content: space-around;
    margin-bottom: 15px;
}

.humidity, .wind {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
}

.location {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Navigation Dots */
.nav-dots {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin: 15px 0;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.dot.active {
    background: var(--primary-color);
    box-shadow: 0 0 15px var(--primary-color);
    border-color: var(--accent-color);
    transform: scale(1.2);
}

.dot:hover {
    background: var(--accent-color);
    transform: scale(1.1);
}

/* Control Panel */
.control-panel {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 10px;
}

.control-btn {
    width: 45px;
    height: 45px;
    border: none;
    border-radius: 50%;
    background: linear-gradient(145deg, var(--primary-color), var(--secondary-color));
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-btn:hover {
    transform: translateY(-2px) rotate(10deg);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
}

.control-btn:active {
    transform: translateY(0) rotate(0deg);
}

.control-btn i {
    font-size: 1.2rem;
}

/* Theme Selector */
.theme-selector {
    position: absolute;
    bottom: 70px;
    left: 50%;
    transform: translateX(-50%) translateY(100%);
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 15px;
    display: flex;
    gap: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.theme-selector.active {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
    visibility: visible;
}

.theme-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    padding: 8px;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.theme-option:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.theme-preview {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

.blue-theme { background: linear-gradient(45deg, #00bcd4, #00e5ff); }
.red-theme { background: linear-gradient(45deg, #f44336, #ff5722); }
.green-theme { background: linear-gradient(45deg, #4caf50, #8bc34a); }
.purple-theme { background: linear-gradient(45deg, #9c27b0, #e91e63); }
.gold-theme { background: linear-gradient(45deg, #ff9800, #ffc107); }

.theme-option span {
    font-size: 0.7rem;
    color: var(--text-color);
    text-align: center;
}

/* Particles Background */
.particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
    border-radius: 50px;
}

.particle {
    position: absolute;
    width: 3px;
    height: 3px;
    background: var(--accent-color);
    border-radius: 50%;
    animation: particleFloat 6s linear infinite;
    opacity: 0.7;
    box-shadow: 0 0 6px var(--accent-color);
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 0.7;
    }
    90% {
        opacity: 0.7;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* Gesture Hint */
.gesture-hint {
    position: absolute;
    bottom: -70px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: 10px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    animation: hintPulse 3s ease-in-out infinite;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 20px;
    border-radius: 25px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes hintPulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

.gesture-hint i {
    color: var(--accent-color);
    animation: pointPulse 2s ease-in-out infinite;
}

@keyframes pointPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* Sound Effects */
.sound-enabled .control-btn.sound-btn {
    background: linear-gradient(145deg, var(--accent-color), var(--primary-color));
}

.sound-disabled .control-btn.sound-btn {
    background: linear-gradient(145deg, #666, #444);
}

/* 3D Floating Elements */
body::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 10% 20%, rgba(255, 218, 185, 0.3) 0%, transparent 30%),
        radial-gradient(circle at 90% 80%, rgba(255, 183, 197, 0.3) 0%, transparent 30%),
        radial-gradient(circle at 70% 30%, rgba(221, 160, 221, 0.3) 0%, transparent 30%),
        radial-gradient(circle at 30% 90%, rgba(255, 234, 167, 0.3) 0%, transparent 30%);
    animation: float3DSecondary 15s ease-in-out infinite reverse;
    z-index: -2;
}

@keyframes float3DSecondary {
    0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-40px) rotate(180deg) scale(1.2);
        opacity: 0.8;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .watch-frame {
        width: 450px;
        height: 580px;
        padding: 25px;
        margin-bottom: 30px;
    }

    .main-display {
        height: 420px;
    }

    .clock-face {
        width: 200px;
        height: 200px;
    }
}

@media (max-width: 480px) {
    .watch-frame {
        width: 400px;
        height: 520px;
        padding: 20px;
        margin-bottom: 25px;
    }

    .main-display {
        height: 380px;
    }

    .time-digits {
        font-size: 3rem;
    }

    .neon-time {
        font-size: 3.5rem;
    }

    .stopwatch-time, .timer-time {
        font-size: 2.5rem;
    }

    .clock-face {
        width: 180px;
        height: 180px;
    }

    .weather-temp {
        font-size: 2rem;
    }

    .weather-icon {
        font-size: 3rem;
    }

    .gesture-hint {
        bottom: -50px;
        font-size: 0.8rem;
        padding: 8px 16px;
    }
}

@media (max-width: 360px) {
    .watch-frame {
        width: 360px;
        height: 480px;
        padding: 18px;
        margin-bottom: 20px;
    }

    .time-digits {
        font-size: 2.5rem;
    }

    .main-display {
        height: 350px;
    }

    .clock-face {
        width: 160px;
        height: 160px;
    }

    .neon-time {
        font-size: 3rem;
    }

    .gesture-hint {
        bottom: -40px;
        font-size: 0.7rem;
        padding: 6px 12px;
    }
}

/* Extra small screens */
@media (max-width: 320px) {
    .watch-frame {
        width: 320px;
        height: 440px;
        padding: 15px;
        margin-bottom: 15px;
    }

    .main-display {
        height: 320px;
    }

    .time-digits {
        font-size: 2rem;
    }

    .clock-face {
        width: 140px;
        height: 140px;
    }

    .neon-time {
        font-size: 2.5rem;
    }

    .stopwatch-time, .timer-time {
        font-size: 2rem;
    }
}

/* Touch Feedback */
.touch-feedback {
    position: relative;
    overflow: hidden;
}

.touch-feedback::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: ripple 0.6s ease-out;
}

@keyframes ripple {
    0% {
        width: 0;
        height: 0;
        opacity: 1;
    }
    100% {
        width: 100px;
        height: 100px;
        opacity: 0;
    }
}

/* Loading Animation */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 3D Floating Shapes Animation */
@keyframes float3DShape {
    0% {
        transform: translateY(100vh) translateX(0px) rotateY(0deg) rotateX(0deg) scale(0.5);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    50% {
        transform: translateY(50vh) translateX(50px) rotateY(180deg) rotateX(90deg) scale(1);
        opacity: 0.8;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) translateX(-30px) rotateY(360deg) rotateX(180deg) scale(0.3);
        opacity: 0;
    }
}

/* Enhanced particle effects */
.floating-shapes {
    perspective: 1000px;
    transform-style: preserve-3d;
}

.floating-shape {
    transform-style: preserve-3d;
    will-change: transform, opacity;
}

/* Additional sweet gradient overlay */
body::after {
    background:
        radial-gradient(circle at 15% 25%, rgba(255, 192, 203, 0.2) 0%, transparent 40%),
        radial-gradient(circle at 85% 75%, rgba(255, 218, 185, 0.2) 0%, transparent 40%),
        radial-gradient(circle at 50% 50%, rgba(221, 160, 221, 0.1) 0%, transparent 50%);
}